// Marketplace Portal - Main Dashboard
// Provides overview of marketplace activity, partnerships, and opportunities

import { defineRoute } from "$fresh/server.ts";
import { Head } from "$fresh/runtime.ts";
import { MarketplaceUser, Partnership, MarketplaceOpportunity } from "../../types/marketplace.ts";
import DashboardLayout from "../../components/layout/DashboardLayout.tsx";
import D3EnhancedRevenueTrend from "../../islands/charts/D3EnhancedRevenueTrend.tsx";
import DataProductsMarketplace from "../../islands/marketplace/DataProductsMarketplace.tsx";
import PremiumMatchingServices from "../../islands/marketplace/PremiumMatchingServices.tsx";
import RevenueAnalytics from "../../islands/marketplace/RevenueAnalytics.tsx";

export default defineRoute((_req, _ctx) => {
  // Mock data for development/testing
  const mockUser: MarketplaceUser = {
    id: "mock-user-1",
    email: "<EMAIL>",
    firstName: "Demo",
    lastName: "User",
    companyName: "Demo Company",
    role: "marketplace_participant",
    tenant_id: "mock-tenant-1",
    isActive: true,
    emailVerified: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    roles: ["marketplace_participant", "partner_seeker"],
    marketplace_tier: "advanced",
    data_sharing_consent: true,
    partner_access_level: "collaborate",
    network_permissions: {
      can_view_benchmarks: true,
      can_initiate_partnerships: true,
      can_access_shared_analytics: true,
      can_create_data_products: false,
      can_manage_revenue_sharing: false
    },
    privacy_settings: {
      allow_partner_discovery: true,
      share_anonymized_metrics: true,
      participate_in_benchmarks: true
    }
  };

  const mockPartnerships: Partnership[] = [
    {
      id: "partnership-1",
      initiator_tenant_id: "mock-tenant-1",
      partner_tenant_id: "partner-1",
      partnership_type: "referral",
      status: "active",
      revenue_share_percentage: 15,
      commission_rate: 5,
      attribution_window_days: 30,
      partnership_terms: {},
      performance_metrics: {},
      created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      activated_at: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString(),
      initiator_company_name: "Demo Company",
      partner_company_name: "TechCorp Solutions",
      compatibility_score: 85
    },
    {
      id: "partnership-2",
      initiator_tenant_id: "mock-tenant-1",
      partner_tenant_id: "partner-2",
      partnership_type: "revenue_sharing",
      status: "active",
      revenue_share_percentage: 20,
      commission_rate: 8,
      attribution_window_days: 45,
      partnership_terms: {},
      performance_metrics: {},
      created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      activated_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString(),
      initiator_company_name: "Demo Company",
      partner_company_name: "RetailMax Inc",
      compatibility_score: 72
    }
  ];

  const mockOpportunities: MarketplaceOpportunity[] = [
    {
      opportunity_id: "opp-1",
      opportunity_type: "partnership",
      title: "High-Value Retail Partnership",
      description: "Partner with leading retail company for cross-promotion opportunities",
      potential_revenue: 25000,
      effort_required: "medium",
      time_to_realize: 30,
      confidence_score: 85,
      recommended_actions: [
        { action: "Initiate contact", priority: "high", estimated_impact: 15000 },
        { action: "Prepare partnership proposal", priority: "medium", estimated_impact: 8000 }
      ],
      related_partners: ["partner-2"]
    },
    {
      opportunity_id: "opp-2",
      opportunity_type: "optimization",
      title: "Conversion Rate Optimization",
      description: "Optimize partner referral conversion rates through A/B testing",
      potential_revenue: 12000,
      effort_required: "low",
      time_to_realize: 14,
      confidence_score: 92,
      recommended_actions: [
        { action: "Set up A/B tests", priority: "high", estimated_impact: 8000 },
        { action: "Analyze conversion funnels", priority: "medium", estimated_impact: 4000 }
      ]
    }
  ];

  const mockPerformanceSummary = {
    active_partnerships: mockPartnerships.filter(p => p.status === 'active').length,
    total_revenue_30d: 45000,
    total_commission_30d: 3200,
    conversion_rate: 12.5,
    top_performing_partnership: mockPartnerships[0]
  };

  // Mock revenue trend data for the enhanced chart
  const mockRevenueTrendData = Array.from({ length: 90 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - (89 - i));
    const baseRevenue = 1000 + Math.sin(i / 10) * 500 + Math.random() * 200;
    return {
      timestamp: date.toISOString(),
      revenue: Math.max(0, baseRevenue + (i * 10)), // Growing trend
      events: Math.floor(50 + Math.random() * 100),
      conversions: Math.floor(5 + Math.random() * 15),
      partners: Math.floor(2 + Math.random() * 3),
      commission: Math.floor(baseRevenue * 0.05)
    };
  });

  const user = mockUser;
  const opportunities = mockOpportunities;
  const performance_summary = mockPerformanceSummary;

  // Check marketplace access
  if (!user.roles.includes('marketplace_participant')) {
    return (
      <DashboardLayout user={user} activeSection="marketplace">
        <Head>
          <title>Marketplace Access Required - E-commerce Analytics</title>
        </Head>
        <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
          <div class="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
            <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
              <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Marketplace Access Required
            </h2>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              Upgrade to Advanced tier or higher to access marketplace features and discover partnership opportunities.
            </p>
            <div class="space-y-3">
              <a 
                href="/settings?tab=billing" 
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 inline-block"
              >
                Upgrade Plan
              </a>
              <a 
                href="/analytics" 
                class="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200 inline-block"
              >
                Back to Analytics
              </a>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout user={user} activeSection="marketplace">
      <Head>
        <title>Marketplace Executive Dashboard - E-commerce Analytics</title>
        <meta name="description" content="Strategic overview of marketplace performance, partnerships, and growth opportunities" />
      </Head>

      <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Executive Header */}
        <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                  Executive Dashboard
                </h1>
                <p class="mt-2 text-gray-600 dark:text-gray-300">
                  Strategic overview of marketplace performance and growth opportunities
                </p>
              </div>

              {/* Executive Quick Actions */}
              <div class="flex space-x-3">
                <a
                  href="/marketplace/discover"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center"
                >
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Discover Partners
                </a>

                <a
                  href="/marketplace/partnerships"
                  class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center"
                >
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Manage Partnerships
                </a>

                <a
                  href="/marketplace/analytics"
                  class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center"
                >
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Analytics Deep Dive
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Executive Dashboard Content */}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

          {/* Revenue Features Overview */}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">

            {/* Data Products Revenue */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Data Products</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">$175K</p>
                  <p class="text-xs text-green-600 dark:text-green-400">ARR Potential</p>
                </div>
              </div>
            </div>

            {/* Premium Matching Revenue */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Premium Matching</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">$160K</p>
                  <p class="text-xs text-green-600 dark:text-green-400">ARR Potential</p>
                </div>
              </div>
            </div>

            {/* Volume Discounts */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Volume Discounts</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">30%</p>
                  <p class="text-xs text-green-600 dark:text-green-400">Max Discount</p>
                </div>
              </div>
            </div>

            {/* Total Revenue Potential */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Revenue</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">$335K</p>
                  <p class="text-xs text-green-600 dark:text-green-400">Additional ARR</p>
                </div>
              </div>
            </div>
          </div>

          {/* Revenue Features Sections */}
          <div class="space-y-8">

            {/* Revenue Analytics */}
            <RevenueAnalytics
              userTier={user.marketplace_tier === 'none' || user.marketplace_tier === 'basic' ? 'core' : user.marketplace_tier}
            />

            {/* Data Products Marketplace */}
            <DataProductsMarketplace
              userTier={user.marketplace_tier === 'none' || user.marketplace_tier === 'basic' ? 'core' : user.marketplace_tier}
            />

            {/* Premium Matching Services */}
            <PremiumMatchingServices
              userTier={user.marketplace_tier === 'none' || user.marketplace_tier === 'basic' ? 'core' : user.marketplace_tier}
            />

          </div>

          {/* Legacy Executive KPI Cards (keeping for reference) */}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 mt-8">

            {/* Total Partnerships KPI */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Partnerships</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">{performance_summary.active_partnerships}</p>
                  <p class="text-xs text-green-600 dark:text-green-400">+2 this month</p>
                </div>
              </div>
            </div>

            {/* 30-Day Revenue KPI */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">30-Day Revenue</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">${(performance_summary.total_revenue_30d / 1000).toFixed(0)}K</p>
                  <p class="text-xs text-green-600 dark:text-green-400">+18.2% vs last month</p>
                </div>
              </div>
            </div>

            {/* Growth Rate KPI */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Growth Rate</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">24.5%</p>
                  <p class="text-xs text-green-600 dark:text-green-400">90-day average</p>
                </div>
              </div>
            </div>

            {/* ROI Metrics KPI */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Partnership ROI</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">340%</p>
                  <p class="text-xs text-green-600 dark:text-green-400">Above industry avg</p>
                </div>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

            {/* Left Column - Revenue Trends & Opportunities */}
            <div class="lg:col-span-2 space-y-8">

              {/* Revenue Trend Chart (90 days) */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Revenue Trends (90 Days)
                  </h2>
                  <p class="mt-1 text-gray-600 dark:text-gray-300">
                    Partnership revenue performance with growth indicators
                  </p>
                </div>
                <div class="p-6">
                  <D3EnhancedRevenueTrend
                    data={mockRevenueTrendData}
                    width={700}
                    height={300}
                    onTimeFrameChange={(timeFrame) => console.log('Time frame changed:', timeFrame)}
                    onMetricChange={(metric) => console.log('Metric changed:', metric)}
                    onDataExport={(data, timeFrame) => console.log('Export data:', data.length, 'points for', timeFrame)}
                  />
                </div>
              </div>

              {/* Top Partnership Opportunities */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                  <div class="flex items-center justify-between">
                    <div>
                      <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Top Partnership Opportunities
                      </h2>
                      <p class="mt-1 text-gray-600 dark:text-gray-300">
                        High-value opportunities with revenue potential
                      </p>
                    </div>
                    <a
                      href="/marketplace/discover"
                      class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                    >
                      View All →
                    </a>
                  </div>
                </div>
                <div class="p-6">
                  <div class="space-y-4">
                    {opportunities.slice(0, 3).map((opportunity, _index) => (
                      <div key={opportunity.opportunity_id} class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex-1">
                          <h3 class="font-medium text-gray-900 dark:text-white">{opportunity.title}</h3>
                          <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{opportunity.description}</p>
                          <div class="flex items-center mt-2 space-x-4">
                            <span class="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full">
                              ${(opportunity.potential_revenue / 1000).toFixed(0)}K potential
                            </span>
                            <span class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                              {opportunity.confidence_score}% confidence
                            </span>
                          </div>
                        </div>
                        <div class="ml-4">
                          <button type="button" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium">
                            Explore
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Network Insights Preview */}
              {user.network_permissions.can_view_benchmarks && (
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                  <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                      <div>
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                          Network Insights
                        </h2>
                        <p class="mt-1 text-gray-600 dark:text-gray-300">
                          Industry benchmarks and trend analysis
                        </p>
                      </div>
                      <a
                        href="/marketplace/insights"
                        class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                      >
                        View All →
                      </a>
                    </div>
                  </div>
                  <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {performance_summary.conversion_rate.toFixed(1)}%
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Conversion Rate
                        </div>
                        <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                          +2.3% vs industry avg
                        </div>
                      </div>
                      <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                          ${(performance_summary.total_revenue_30d / 1000).toFixed(1)}K
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Revenue (30d)
                        </div>
                        <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                          +15.7% vs last month
                        </div>
                      </div>
                      <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                          {performance_summary.active_partnerships}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Active Partners
                        </div>
                        <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                          +{Math.floor(performance_summary.active_partnerships * 0.2)} this month
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Recent Activity & Executive Actions */}
            <div class="space-y-8">

              {/* Recent Activity Timeline */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Recent Activity
                  </h2>
                  <p class="mt-1 text-gray-600 dark:text-gray-300">
                    Last 7 days of partnership events
                  </p>
                </div>
                <div class="p-6">
                  <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                      <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">New partnership activated</p>
                        <p class="text-xs text-gray-600 dark:text-gray-300">RetailMax Inc - Revenue sharing agreement</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">2 days ago</p>
                      </div>
                    </div>
                    <div class="flex items-start space-x-3">
                      <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Revenue milestone reached</p>
                        <p class="text-xs text-gray-600 dark:text-gray-300">$50K monthly revenue achieved</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">4 days ago</p>
                      </div>
                    </div>
                    <div class="flex items-start space-x-3">
                      <div class="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                      <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Partnership opportunity identified</p>
                        <p class="text-xs text-gray-600 dark:text-gray-300">High-value retail partnership - $25K potential</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">6 days ago</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Executive Summary Stats */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Executive Summary
                </h3>
                <div class="space-y-4">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-300">Commission Earned</span>
                    <span class="font-semibold text-gray-900 dark:text-white">
                      ${performance_summary.total_commission_30d.toLocaleString()}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-300">Conversion Rate</span>
                    <span class="font-semibold text-green-600 dark:text-green-400">
                      {performance_summary.conversion_rate}%
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-300">Top Partnership</span>
                    <span class="font-semibold text-gray-900 dark:text-white text-sm">
                      {performance_summary.top_performing_partnership?.partner_company_name || 'N/A'}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-300">Marketplace Tier</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 capitalize">
                      {user.marketplace_tier}
                    </span>
                  </div>
                </div>
              </div>

              {/* Navigation Links */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Marketplace Tools
                </h3>
                <div class="space-y-3">
                  <a
                    href="/marketplace/discover"
                    class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <span class="text-gray-900 dark:text-white font-medium">Partner Discovery</span>
                  </a>
                  
                  <a
                    href="/marketplace/partnerships"
                    class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <span class="text-gray-900 dark:text-white font-medium">Manage Partnerships</span>
                  </a>
                  
                  {user.network_permissions.can_access_shared_analytics && (
                    <a
                      href="/marketplace/collaborate"
                      class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                    >
                      <svg class="w-5 h-5 text-purple-600 dark:text-purple-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <span class="text-gray-900 dark:text-white font-medium">Collaborative Analytics</span>
                    </a>
                  )}
                  
                  <a
                    href="/marketplace/settings"
                    class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    <svg class="w-5 h-5 text-gray-600 dark:text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span class="text-gray-900 dark:text-white font-medium">Marketplace Settings</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
});
