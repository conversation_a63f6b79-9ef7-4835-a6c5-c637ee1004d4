// Premium Matching Services API Endpoints
// Pay-per-introduction billing system with success tracking
// Target: $160K ARR from premium matching revenue stream

import { Handlers } from "$fresh/server.ts";
import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { DataProductsMarketplace } from "../../../../utils/dataProductsMarketplace.ts";

interface PremiumMatchingRequest {
  requester_tier: 'advanced' | 'enterprise' | 'strategic';
  target_industry?: string;
  target_geography?: string;
  target_company_size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  target_revenue_range?: string;
  matching_criteria: Record<string, unknown>;
  service_type: 'introduction' | 'consultation' | 'custom_matching';
  success_criteria?: Record<string, unknown>;
}

interface PremiumMatchingResponse {
  success: boolean;
  data?: unknown;
  error?: string;
  processing_time_ms?: number;
  metadata?: {
    service_price?: number;
    estimated_completion_time?: string;
    success_bonus_potential?: number;
    performance_target_met?: boolean;
    total_count?: number;
    page_size?: number;
    current_page?: number;
  };
}

// Database connection factory
function createClient(): Client {
  return new Client({
    user: "postgres",
    password: "password",
    database: "ecommerce_analytics",
    hostname: "localhost",
    port: 5432,
  });
}

const handler: Handlers = {
  /**
   * POST /api/marketplace/premium-matching
   * Create a premium matching request
   */
  async POST(req, _ctx) {
    const startTime = performance.now();
    const client = createClient();
    
    try {
      await client.connect();
      
      const marketplace = DataProductsMarketplace.getInstance(client);
      
      const body: PremiumMatchingRequest = await req.json();
      
      // Validate required fields
      if (!body.requester_tier || !body.matching_criteria || !body.service_type) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing required fields: requester_tier, matching_criteria, service_type"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // For now, use a placeholder requester tenant ID - in production this would come from authentication
      const requesterTenantId = crypto.randomUUID();

      const matchingRequest = await marketplace.createPremiumMatchingRequest(
        requesterTenantId,
        body.requester_tier,
        {
          target_industry: body.target_industry,
          target_geography: body.target_geography,
          target_company_size: body.target_company_size,
          target_revenue_range: body.target_revenue_range,
          matching_criteria: body.matching_criteria,
          service_type: body.service_type,
          success_criteria: body.success_criteria
        }
      );

      const processingTime = performance.now() - startTime;
      const performanceTargetMet = processingTime <= 100;

      // Calculate estimated completion time based on service type
      const estimatedHours = {
        introduction: 24,
        consultation: 48,
        custom_matching: 72
      };

      const estimatedCompletion = new Date();
      estimatedCompletion.setHours(estimatedCompletion.getHours() + estimatedHours[body.service_type]);

      // Calculate potential success bonus (10-25% of service price)
      const successBonusPotential = Math.round(matchingRequest.service_price * 0.175); // Average 17.5%

      const response: PremiumMatchingResponse = {
        success: true,
        data: matchingRequest,
        processing_time_ms: Math.round(processingTime),
        metadata: {
          service_price: matchingRequest.service_price,
          estimated_completion_time: estimatedCompletion.toISOString(),
          success_bonus_potential: successBonusPotential,
          performance_target_met: performanceTargetMet
        }
      };

      return new Response(JSON.stringify(response), {
        status: 201,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Premium matching request creation failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  },

  /**
   * GET /api/marketplace/premium-matching?requester_id=xxx
   * Get premium matching requests for a tenant
   */
  async GET(req, _ctx) {
    const startTime = performance.now();
    const client = createClient();
    
    try {
      await client.connect();
      
      const url = new URL(req.url);
      const requesterId = url.searchParams.get("requester_id");
      const status = url.searchParams.get("status");
      const limit = parseInt(url.searchParams.get("limit") || "20");
      const offset = parseInt(url.searchParams.get("offset") || "0");

      if (!requesterId) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing required parameter: requester_id"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      let query = `
        SELECT * FROM marketplace_premium_matching
        WHERE requester_tenant_id = $1
      `;
      const params: unknown[] = [requesterId];
      let paramIndex = 2;

      if (status) {
        query += ` AND status = $${paramIndex}`;
        params.push(status);
        paramIndex++;
      }

      query += ` ORDER BY created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      params.push(limit, offset);

      const result = await client.queryObject(query, params);

      // Get total count
      let countQuery = `
        SELECT COUNT(*) as total FROM marketplace_premium_matching
        WHERE requester_tenant_id = $1
      `;
      const countParams = [requesterId];
      
      if (status) {
        countQuery += ` AND status = $2`;
        countParams.push(status);
      }

      const countResult = await client.queryObject(countQuery, countParams);
      const total = Number(countResult.rows[0].total);

      const processingTime = performance.now() - startTime;

      const response: PremiumMatchingResponse = {
        success: true,
        data: result.rows,
        processing_time_ms: Math.round(processingTime),
        metadata: {
          total_count: total,
          page_size: limit,
          current_page: Math.floor(offset / limit) + 1,
          performance_target_met: processingTime <= 100
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Premium matching requests retrieval failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  }
};

export default handler;
